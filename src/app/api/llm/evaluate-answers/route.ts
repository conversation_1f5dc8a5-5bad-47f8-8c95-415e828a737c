import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { AnswerEvaluationResult } from '@/backend/services';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'node:fs/promises';
import path from 'node:path';
import { z, ZodError } from 'zod';

const evaluateAnswersSchema = z.object({
	paragraph: z.string().min(1, 'Paragraph is required'),
	questions: z.array(z.string()).min(1, 'At least one question is required'),
	answers: z.array(z.string()).min(1, 'At least one answer is required'),
	qna_language: z.nativeEnum(Language),
	feedback_native_language: z.nativeEnum(Language),
});

export async function POST(request: NextRequest) {
	let userId: string | undefined;
	try {
		const session = await auth();
		userId = session?.user?.id;
		if (!userId) {
			throw new UnauthorizedError(
				'Unauthorized: User must be authenticated to evaluate answers.'
			);
		}

		const body = await request.json();
		const validatedData = evaluateAnswersSchema.parse(body);
		const { paragraph, questions, answers, qna_language, feedback_native_language } =
			validatedData;

		if (questions.length !== answers.length) {
			throw new ValidationError('Number of questions must match number of answers');
		}

		// Create cache file path for development
		// File-based cache disabled in development
		// const cacheDir = path.join(process.cwd(), '.cache', 'llm');
		// const cacheKey = `answer-eval-${Buffer.from(
		// 	paragraph + questions.join('') + answers.join('')
		// )
		// 	.toString('base64')
		// 	.slice(0, 20)}-${qna_language}`;
		// const filePath = path.join(cacheDir, `${cacheKey}.json`);

		// File-based cache read disabled in development
		// if (process.env.NODE_ENV === 'development') {
		// 	try {
		// 		const cachedData = await fs.readFile(filePath, 'utf-8');
		// 		const parsedData = JSON.parse(cachedData);
		// 		return NextResponse.json(parsedData);
		// 	} catch (err) {
		// 		// Cache miss, continue with generation
		// 	}
		// }

		const llmService = await getLLMService();
		const evaluations = await llmService.evaluateAnswers({
			paragraph,
			questions,
			answers,
			qna_language,
			feedback_native_language,
		});

		// File-based cache write disabled in development
		// if (process.env.NODE_ENV === 'development') {
		// 	try {
		// 		await fs.mkdir(cacheDir, { recursive: true });
		// 		await fs.writeFile(filePath, JSON.stringify(evaluations, null, 2), 'utf-8');
		// 	} catch (err: any) {
		// 		console.error(
		// 			`Development mode: Failed to save answer evaluation cache file ${filePath}:`,
		// 			err.message
		// 		);
		// 	}
		// }

		return NextResponse.json(evaluations);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error(`Error in evaluateAnswersApi for user ${userId || 'unknown'}:`, error);
		return NextResponse.json(
			{ error: 'Failed to evaluate answers. Please try again.' },
			{ status: 500 }
		);
	}
}
