import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { GrammarPracticeResultItem } from '@/backend/services';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { Difficulty, Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'node:fs/promises';
import path from 'node:path';
import { z, ZodError } from 'zod';

const grammarPracticeSchema = z.object({
	keywords: z
		.array(z.string())
		.min(1, 'At least one keyword is required')
		.max(10, 'Cannot use more than 10 keywords'),
	language: z.nativeEnum(Language),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
	difficulty: z.nativeEnum(Difficulty),
	count: z
		.number()
		.min(1, 'Count must be at least 1')
		.max(5, 'Cannot generate more than 5 practice items at once'),
	sentenceCount: z.number().min(1).max(30).optional(),
	errorDensity: z.enum(['low', 'medium', 'high']).optional(),
});

export async function POST(request: NextRequest) {
	let userId: string | undefined;
	try {
		const session = await auth();
		userId = session?.user?.id;
		if (!userId)
			throw new UnauthorizedError('User not authenticated for generating paragraphs.');

		const body = await request.json();
		const validatedData = grammarPracticeSchema.parse(body);
		const {
			keywords,
			language,
			source_language,
			target_language,
			difficulty,
			count,
			sentenceCount,
			errorDensity,
		} = validatedData;

		// Create cache file path for development
		// File-based cache disabled in development
		// const cacheDir = path.join(process.cwd(), '.cache', 'llm');
		// const cacheKey = `grammar-practice-${keywords.join(
		// 	'-'
		// )}-${language}-${source_language}-${target_language}-${difficulty}-${count}-${
		// 	sentenceCount || 'default'
		// }-${errorDensity || 'medium'}`;
		// const filePath = path.join(cacheDir, `${cacheKey}.json`);

		// File-based cache read disabled in development
		// if (process.env.NODE_ENV === 'development') {
		// 	try {
		// 		const cachedData = await fs.readFile(filePath, 'utf-8');
		// 		const parsedData = JSON.parse(cachedData);
		// 		return NextResponse.json(parsedData);
		// 	} catch (err) {
		// 		// Cache miss, continue with generation
		// 	}
		// }

		const llmService = await getLLMService();
		const result = await llmService.generateGrammarPractice({
			keywords,
			language,
			source_language,
			target_language,
			difficulty,
			count,
			sentenceCount,
			errorDensity,
		});

		// File-based cache write disabled in development
		// if (process.env.NODE_ENV === 'development') {
		// 	try {
		// 		await fs.mkdir(cacheDir, { recursive: true });
		// 		await fs.writeFile(filePath, JSON.stringify(result, null, 2), 'utf-8');
		// 	} catch (err: any) {
		// 		console.error(
		// 			`Development mode: Failed to save grammar practice cache file ${filePath}:`,
		// 			err.message
		// 		);
		// 	}
		// }

		return NextResponse.json(result);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error(
			`Error in generateGrammarPracticeApi for user ${userId || 'unknown'}:`,
			error
		);
		return NextResponse.json(
			{ error: 'Failed to generate grammar practice. Please try again.' },
			{ status: 500 }
		);
	}
}
