import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { TranslationEvaluationResult } from '@/backend/services';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'node:fs/promises';
import path from 'node:path';
import { z, ZodError } from 'zod';

const evaluateTranslationSchema = z.object({
	original_text: z.string().min(1, 'Original text is required'),
	translated_text: z.string().min(1, 'User translation is required'),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
});

export async function POST(request: NextRequest) {
	let userId: string | undefined;
	try {
		const session = await auth();
		userId = session?.user?.id;
		if (!userId) {
			throw new UnauthorizedError(
				'Unauthorized: User must be authenticated to evaluate translation.'
			);
		}

		const body = await request.json();
		const validatedData = evaluateTranslationSchema.parse(body);
		const { original_text, translated_text, source_language, target_language } = validatedData;

		// Create cache file path for development
		// File-based cache disabled in development
		// const cacheDir = path.join(process.cwd(), '.cache', 'llm');
		// const cacheKey = `translation-eval-${Buffer.from(original_text + translated_text)
		// 	.toString('base64')
		// 	.slice(0, 20)}-${source_language}-${target_language}`;
		// const filePath = path.join(cacheDir, `${cacheKey}.json`);

		// File-based cache read disabled in development
		// if (process.env.NODE_ENV === 'development') {
		// 	try {
		// 		const cachedData = await fs.readFile(filePath, 'utf-8');
		// 		const parsedData = JSON.parse(cachedData);
		// 		return NextResponse.json(parsedData);
		// 	} catch (err) {
		// 		// Cache miss, continue with generation
		// 	}
		// }

		const llmService = await getLLMService();
		const evaluation = await llmService.evaluateTranslation({
			original_text,
			translated_text,
			source_language,
			target_language,
		});

		// File-based cache write disabled in development
		// if (process.env.NODE_ENV === 'development') {
		// 	try {
		// 		await fs.mkdir(cacheDir, { recursive: true });
		// 		await fs.writeFile(filePath, JSON.stringify(evaluation, null, 2), 'utf-8');
		// 	} catch (err: any) {
		// 		console.error(
		// 			`Development mode: Failed to save translation evaluation cache file ${filePath}:`,
		// 			err.message
		// 		);
		// 	}
		// }

		return NextResponse.json(evaluation);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error(`Error in evaluateTranslationApi for user ${userId || 'unknown'}:`, error);
		return NextResponse.json(
			{ error: 'Failed to evaluate translation. Please try again.' },
			{ status: 500 }
		);
	}
}
