import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { ParagraphWithQuestionsResult } from '@/backend/services';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { Difficulty, Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'node:fs/promises';
import path from 'node:path';
import { z, ZodError } from 'zod';

const generateParagraphWithQuestionsSchema = z.object({
	keywords: z
		.array(z.string())
		.min(1, 'At least one keyword is required')
		.max(10, 'Cannot use more than 10 keywords'),
	language: z.nativeEnum(Language),
	difficulty: z.nativeEnum(Difficulty),
	sentenceCount: z.number().min(1).max(30).optional(),
	questionCount: z
		.number()
		.min(1, 'Question count must be at least 1')
		.max(10, 'Cannot generate more than 10 questions'),
});

export async function POST(request: NextRequest) {
	let userId: string | undefined;
	try {
		const session = await auth();
		userId = session?.user?.id;
		if (!userId) {
			throw new UnauthorizedError(
				'Unauthorized: User must be authenticated to generate paragraph with questions.'
			);
		}

		const body = await request.json();
		const validatedData = generateParagraphWithQuestionsSchema.parse(body);
		const { keywords, language, difficulty, sentenceCount, questionCount } = validatedData;

		// Create cache file path for development
		// File-based cache disabled in development
		// const cacheDir = path.join(process.cwd(), '.cache', 'llm');
		// const cacheKey = `paragraph-questions-${keywords.join('-')}-${language}-${difficulty}-${
		// 	sentenceCount || 'default'
		// }-${questionCount}`;
		// const filePath = path.join(cacheDir, `${cacheKey}.json`);

		// File-based cache read disabled in development
		// if (process.env.NODE_ENV === 'development') {
		// 	try {
		// 		const cachedData = await fs.readFile(filePath, 'utf-8');
		// 		const parsedData = JSON.parse(cachedData);
		// 		return NextResponse.json(parsedData as ParagraphWithQuestionsResult);
		// 	} catch (err) {
		// 		// Cache miss, continue with generation
		// 	}
		// }

		const llmService = await getLLMService();
		const result = await llmService.generateParagraphWithQuestions({
			keywords,
			language,
			difficulty,
			sentenceCount,
			questionCount,
		});

		// File-based cache write disabled in development
		// if (process.env.NODE_ENV === 'development') {
		// 	try {
		// 		await fs.mkdir(cacheDir, { recursive: true });
		// 		await fs.writeFile(filePath, JSON.stringify(result, null, 2), 'utf-8');
		// 	} catch (err: any) {
		// 		console.error(
		// 			`Development mode: Failed to save paragraph with questions cache file ${filePath}:`,
		// 			err.message
		// 		);
		// 	}
		// }

		return NextResponse.json(result);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error(
			`Error in generateParagraphWithQuestionsApi for user ${userId || 'unknown'}:`,
			error
		);
		return NextResponse.json(
			{ error: 'Failed to generate paragraph with questions. Please try again.' },
			{ status: 500 }
		);
	}
}
